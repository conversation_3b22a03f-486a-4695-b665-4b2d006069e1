from itertools import chain, starmap
from uuid import UUID

from fastapi import (
    <PERSON><PERSON><PERSON><PERSON>,
    Depends,
    HTTPException,
    Path,
    Query,
    Request,
    Response,
    status,
)
from pydantic import PositiveInt

import api.deps as trace_id_deps
from accounts.domain.dto import Account
from accounts.services import MediaService
from api.accounts.deps import get_media_service
from api.authorization.deps import authorization_service
from api.billing import deps
from api.billing.schemas import (
    Adjustment,
    BillingCycle,
    CreateAdjustment,
    GetInvoices,
    Invoice,
    InvoiceNew,
    Reconciliation,
    SIMCardsTotalUsage,
    SIMCardUsage,
    SIMDetails,
    UpdateAdjustment,
)
from api.decorators import (
    check_permissions,
    convert_response_to_csv_with_panda,
    measure_execution_time,
)
from api.deps import distributor_access_level, get_authenticated_user
from api.renders import make_csv_renderer
from api.resolvers import Resolver
from api.responses import CSVFileResponse
from app.config import logger
from authorization.domain.ports import AbstractAuthorizationAPI
from billing.domain.model import AdjustmentType
from billing.exceptions import (
    BillingCycleDoesNotExist,
    BillingObjectDoesNotExist,
    CalculationIncomplete,
    CalculationInProgress,
    InvoiceAlreadyPublished,
    InvoiceDoesNotExist,
    InvoiceHasNotBeenPublished,
)
from billing.reports import InvoiceChargesReport, Record
from billing.services import BillingService
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import EnumerationList, Month

router = APIRouter(tags=["billing"], prefix="/billing")


@router.get(
    "/invoices/{id}",
    response_model=Invoice,
    response_model_by_alias=True,
)
@check_permissions
def invoice_details(
    request: Request,
    invoice_id: int = Path(alias="id"),
    billing_service: BillingService = Depends(deps.billing_service),
    account_resolver: Resolver[int, Account] = Depends(deps.account_resolver),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Invoice:
    """Get invoice details by ID."""
    try:
        invoice = billing_service.get_invoice(invoice_id)
        get_account = account_resolver([invoice.account_id])
        account = get_account(invoice.account_id)
    except InvoiceDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    else:
        return Invoice.from_model(invoice, account)


@router.put(
    "/invoices/{id}/published",
    response_model=Invoice,
    response_model_by_alias=True,
)
@check_permissions
def publish_invoice(
    request: Request,
    invoice_id: int = Path(alias="id"),
    billing_service: BillingService = Depends(deps.billing_service),
    account_resolver: Resolver[int, Account] = Depends(deps.account_resolver),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    """Publish invoice by ID."""
    try:
        published_invoices, account_ids = billing_service.publish_invoice([invoice_id])
        get_account = account_resolver(list(account_ids))
    except InvoiceDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except (CalculationIncomplete, InvoiceAlreadyPublished) as e:
        raise HTTPException(status.HTTP_409_CONFLICT, str(e))
    else:
        invoice = next(iter(published_invoices), None)
        return Invoice.from_model(
            invoice, get_account(invoice.account_id)  # type: ignore
        )


@router.patch(
    "/invoices/publish",
    response_model=list[Invoice],
    response_model_by_alias=True,
)
@check_permissions
@measure_execution_time
def publish_multiple_invoice(
    request: Request,
    invoice_ids: list[PositiveInt],
    billing_service: BillingService = Depends(deps.billing_service),
    account_resolver: Resolver[int, Account] = Depends(deps.account_resolver),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    """Publish all invoice by ID."""
    try:
        published_invoices, account_ids = billing_service.publish_invoice(invoice_ids)
    except InvoiceDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except (CalculationIncomplete, InvoiceAlreadyPublished) as e:
        raise HTTPException(status.HTTP_409_CONFLICT, str(e))
    else:
        logger.critical(f"Account_ids: {account_ids}")
        get_account = account_resolver(list(account_ids))
        return [
            Invoice.from_model(invoice, get_account(invoice.account_id))
            for invoice in published_invoices
        ]


@router.delete(
    "/invoices/{id}/published",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
)
@check_permissions
@measure_execution_time
def unpublish_invoice(
    request: Request,
    invoice_id: int = Path(alias="id"),
    billing_service: BillingService = Depends(deps.billing_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    """Set invoice as unpublished by ID."""
    try:
        billing_service.unpublish_invoice(invoice_id)
    except InvoiceDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except (CalculationIncomplete, InvoiceHasNotBeenPublished) as e:
        raise HTTPException(status.HTTP_409_CONFLICT, str(e))


@check_permissions
def invoice_usage(
    request: Request,
    invoice_id: int = Path(alias="id"),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            SIMCardUsage,
            default_ordering="iccid",
            ordering_fields=(
                "iccid",
                "id",
                "msisdn",
                "imsi",
                "rate_plan_name",
                "subscription_charge",
                "plan_data_charge",
                "roaming_data_charge",
                "plan_data_volume",
                "roaming_data_volume",
                "data_charge",
                "voice_charge",
                "sms_charge",
                "data_volume",
                "sms_volume",
                "sms_mo_volume",
                "sms_mt_volume",
                "voice_volume",
                "voice_mo_volume",
                "voice_mt_volume",
            ),
        )
    ),
    billing_service: BillingService = Depends(deps.billing_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "iccid",
                "msisdn",
                "imsi",
                "sim_id",
                "subscription.name",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> SIMDetails:
    try:
        invoice = billing_service.get_invoice(invoice_id)
        sim_cards, total_count = billing_service.get_sim_usage(
            invoice, pagination=pagination, ordering=ordering, searching=searching
        )
        service_usage, subscription_charge = billing_service.get_sim_total_usage(
            invoice, searching=searching
        )
    except InvoiceDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    else:
        return SIMDetails.build(
            pagination=pagination,
            results=map(SIMCardUsage.from_model, sim_cards),
            total_count=total_count,
            total_usage=SIMCardsTotalUsage(
                service_usage=service_usage, subscription_charge=subscription_charge
            ),
        )


router.get(
    "/invoices/{id}/usage",
    response_model=SIMDetails,
    response_model_by_alias=True,
)(invoice_usage)


@check_permissions
@measure_execution_time
def invoice_usage_export(
    request: Request,
    invoice_id: int = Path(alias="id"),
    billing_service: BillingService = Depends(deps.billing_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> list[dict]:
    try:
        invoice = billing_service.get_invoice(invoice_id)
        sim_cards = billing_service.get_sim_usage_export(invoice)
    except InvoiceDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    else:
        return sim_cards


router.get(
    "/invoices/{id}/usage/export",
    summary="Export Invoice usage to csv",
    response_class=CSVFileResponse,
)(
    convert_response_to_csv_with_panda(list[dict], "SIMDetailsExport")(
        invoice_usage_export
    )
)


@router.get(
    "/adjustment-types",
    response_model=EnumerationList,
    dependencies=[Depends(get_authenticated_user)],
)
def adjustment_types():
    return EnumerationList.from_enum(AdjustmentType)


@router.post(
    "/invoices/{id}/adjustments",
    status_code=status.HTTP_201_CREATED,
    response_model=Adjustment,
)
@check_permissions
def create_adjustment(
    request: Request,
    adjustment_input: CreateAdjustment,
    invoice_id: int = Path(alias="id"),
    billing_service: BillingService = Depends(deps.billing_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Adjustment:
    try:
        adjustment = billing_service.add_invoice_adjustment(
            invoice_id, adjustment_input.to_model()
        )
    except InvoiceDoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    except (CalculationIncomplete, InvoiceAlreadyPublished) as e:
        raise HTTPException(status.HTTP_409_CONFLICT, str(e))
    else:
        return Adjustment.from_model(adjustment)


@router.delete(
    "/invoices/{invoice_id}/adjustments/{id}",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
)
@check_permissions
def remove_adjustment(
    request: Request,
    invoice_id: int,
    adjustment_id: int = Path(alias="id"),
    billing_service: BillingService = Depends(deps.billing_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> None:
    try:
        billing_service.remove_invoice_adjustment(invoice_id, adjustment_id)
    except BillingObjectDoesNotExist as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, str(e))
    except (CalculationIncomplete, InvoiceAlreadyPublished) as e:
        raise HTTPException(status.HTTP_409_CONFLICT, str(e))


@router.put(
    "/invoices/{invoice_id}/adjustments/{id}",
    response_model=Adjustment,
    dependencies=[Depends(distributor_access_level())],
)
@check_permissions
def update_adjustment(
    request: Request,
    adjustment_input: UpdateAdjustment,
    invoice_id: int,
    adjustment_id: int = Path(alias="id"),
    billing_service: BillingService = Depends(deps.billing_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Adjustment:
    try:
        if not adjustment_input.id == adjustment_id:
            raise HTTPException(
                status.HTTP_400_BAD_REQUEST,
                detail="Attempt to change the `id` of the Invoice Adjustment",
            )
        adjustment = adjustment_input.to_model(id=adjustment_id)
        billing_service.update_invoice_adjustment(invoice_id, adjustment)
    except BillingObjectDoesNotExist as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, str(e))
    except (CalculationIncomplete, InvoiceAlreadyPublished) as e:
        raise HTTPException(status.HTTP_409_CONFLICT, str(e))
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )
    else:
        return Adjustment.from_model(adjustment)


@router.get(
    "/invoices-old",
    response_model=list[Invoice],
    response_model_by_alias=True,
    deprecated=True,
)
@measure_execution_time
def get_invoices(
    request: Request,
    month: Month | None = None,
    billing_service: BillingService = Depends(deps.billing_service),
    account_resolver: Resolver[int, Account] = Depends(deps.account_resolver),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> list[Invoice]:
    try:
        invoices = list(billing_service.get_invoices(month))
        get_account = account_resolver((i.account_id for i in invoices))

        invoices_with_accounts = ((i, get_account(i.account_id)) for i in invoices)
        return list(starmap(Invoice.from_model, invoices_with_accounts))
    except BillingCycleDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.get(
    "/invoices",
    response_model=list[InvoiceNew],
    response_model_by_alias=True,
)
@check_permissions
@measure_execution_time
def get_invoices_new(
    request: Request,
    month: Month | None = None,
    billing_service: BillingService = Depends(deps.billing_service),
    media_service: MediaService = Depends(get_media_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> list[InvoiceNew]:
    try:
        invoices = list(billing_service.get_invoices_new(month))
        result = GetInvoices.from_model(invoices, media_service)
        return result.invoices
    except BillingCycleDoesNotExist as e:
        logger.error(f"Billing cycle does not exist: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.get(
    "/cycles/{month}",
    response_model=BillingCycle,
    response_model_by_alias=True,
    dependencies=[Depends(distributor_access_level())],
    deprecated=True,
)
def get_billing_cycle(
    month: Month,
    billing_service: BillingService = Depends(deps.billing_service),
    account_resolver: Resolver[int, Account] = Depends(deps.account_resolver),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> BillingCycle:
    try:
        invoices = list(billing_service.get_invoices(month))
        get_account = account_resolver((i.account_id for i in invoices))
        invoices_with_accounts = ((i, get_account(i.account_id)) for i in invoices)
        return BillingCycle(
            month=month,
            invoices=list(starmap(Invoice.from_model, invoices_with_accounts)),
        )
    except BillingCycleDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.post(
    "/cycles/{month}",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
)
@check_permissions
def generate_billing_cycle_invoices(
    request: Request,
    month: Month,
    billing_service: BillingService = Depends(deps.billing_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
    account_id: int | None = Query(None, ge=1),
) -> None:
    """Generate billing cycle invoices."""
    account_ids = [account_id] if account_id is not None else None
    try:
        billing_service.generate_invoices(month, account_ids)
    except CalculationInProgress as e:
        raise HTTPException(status.HTTP_409_CONFLICT, str(e))


@router.delete(
    "/cycles/{month}",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
    deprecated=True,
)
def remove_billing_cycle_invoices(
    month: Month,
    billing_service: BillingService = Depends(deps.billing_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> None:
    """Remove the billing cycle unpublished invoices."""
    try:
        billing_service.remove_invoices(month)
    except BillingCycleDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except CalculationInProgress as e:
        raise HTTPException(status.HTTP_409_CONFLICT, str(e))


@router.get(
    "/cycles/{month}/report",
    response_class=CSVFileResponse,
    dependencies=[Depends(distributor_access_level())],
)
def billing_cycle_report(
    month: Month,
    billing_service: BillingService = Depends(deps.billing_service),
    account_resolver: Resolver[int, Account] = Depends(deps.account_resolver),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    """Get Invoice Charges Report for the billing cycle."""

    try:
        invoices = list(billing_service.get_invoices(month))
        get_account = account_resolver((i.account_id for i in invoices))
        account_names = {i.account_id: get_account(i.account_id).name for i in invoices}
        report = InvoiceChargesReport(month, account_names)
        records = chain.from_iterable(map(report.invoice_records, invoices))
        render = make_csv_renderer(Record)
        return CSVFileResponse(
            content=render(list(records)),
            filename=f"Invoice Charges Report {month}.csv",
        )
    except BillingCycleDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.get(
    "/invoices/{month}/reconciliation",
    status_code=status.HTTP_200_OK,
)
def invoice_reconciliation(
    month: Month,
    billing_service: BillingService = Depends(deps.billing_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Reconciliation:
    try:
        response = billing_service.invoice_reconciliation(month)
        return Reconciliation(result=response)
    except BillingCycleDoesNotExist as e:
        logger.error(f"Billing cycle does not exist.: {(str(e))}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND, detail="Billing cycle does not exist."
        )


@router.get(
    "/monthly/{month}/reconciliation",
    status_code=status.HTTP_200_OK,
)
def monthly_reconciliation(
    month: Month,
    billing_service: BillingService = Depends(deps.billing_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Reconciliation:
    try:
        response = billing_service.get_monthly_reconciliation(month)
        return Reconciliation(result=response)
    except BillingCycleDoesNotExist as e:
        logger.error(f"Billing cycle does not exist.: {(str(e))}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND, detail="Billing cycle does not exist."
        )
