import random
from abc import ABC
from collections.abc import Sequence
from dataclasses import dataclass
from datetime import date
from decimal import Decimal
from typing import Iterable
from unittest.mock import MagicMock

import more_itertools

from accounts.adapters.repository import (
    AbstractAccountRepository,
    InMemoryAccountRepository,
)
from accounts.domain.model import (
    Account,
    AccountStatus,
    PaymentTerms,
    SalesChannel,
    SimProfile,
)
from api.billing.examples import (
    SIM_DATA_1,
    SIM_DATA_2,
    SIM_DATA_3,
    SIM_DATA_4,
    SIM_DATA_5,
    SIM_DATA_6,
    SIM_DATA_7,
    SIM_DATA_8,
)
from billing.adapters.repository import (
    AbstractBillingCycleRepository,
    InMemoryBillingCycleRepository,
)
from billing.domain.model import (
    BillingCycle,
    InvoiceRatingState,
    ReconciliationAgg,
    ReconciliationDetails,
    SubscriptionSIM,
)
from billing.exceptions import BillingCycleDoesNotExist
from billing.services import AbstractSimUsageService, BillingService, SimUsageService
from common.constants import GB
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import ICCID, IMSI, Month, Service
from rate_plans.adapters.rate_plan_repository import (
    AbstractRatePlanRepository,
    InMemoryRatePlanRepository,
)
from rate_plans.domain.model import Rate, RatePlan, RatePlanModel, RatePlans
from rating.adapters.usage_repository import (
    AbstractUsageRepository,
    InMemoryUsageRepository,
)
from rating.calculation import RatingCalculator
from rating.domain.model import MonthlyUsageRecords
from sim.adapters.repository import AbstractSimRepository, InMemorySimRepository
from sim.domain import model as sim_model
from sim.domain.model import MSISDN, Allocation, SimStatus

import pytest  # isort: skip


@dataclass
class RatesPlanTest:
    rate_model: int
    rates: Sequence["Rate"]


class AbstractBillingServiceTestCase(ABC):
    @pytest.fixture
    def usage_repository(self, *args, **kwargs) -> AbstractUsageRepository:
        raise NotImplementedError()

    @pytest.fixture
    def sim_repository(self, *args, **kwargs) -> AbstractSimRepository:
        raise NotImplementedError()

    @pytest.fixture
    def billing_service(self, *args, **kwargs) -> BillingService:
        raise NotImplementedError()

    @pytest.fixture
    def account_repository(self, *args, **kwargs) -> AbstractAccountRepository:
        raise NotImplementedError()

    @pytest.fixture
    def accounts(self, account_repository):
        accounts = [
            Account(
                name=f"Vodafone-{i}",
                status=AccountStatus.ACTIVE,
                agreement_number="Test",
                currency="UAH",
                product_types=["NATIONAL_ROAMING"],
                industry_vertical="TELECOMMUNICATIONS",
                sales_person="John Doe",
                is_billable=True,
                contract_end_date=date.today(),
                sales_channel=SalesChannel.WHOLESALE,
                payment_terms=PaymentTerms(30),
                sim_charge=Decimal(0),
                organization_id=1,
                sim_profile=SimProfile.DATA_ONLY,
            )
            for i in range(1, 1 + 3)
        ]
        for account in accounts:
            account_repository.add(account)
        return accounts

    @pytest.fixture
    def account(self, accounts) -> Account:
        return accounts[0]

    @pytest.fixture
    def rate_plan(self, account):
        rate_plan = RatePlan(
            account_id=account.id,
            name="PayG",
            access_fee=Decimal("2.22"),
            is_default=False,
            sim_limit=Decimal(0),
        )
        rate_plan.data = RatesPlanTest(
            rate_model=1,
            rates=[
                Rate(
                    range_from=0,
                    range_to=None,
                    value=Decimal("2.11"),
                    range_unit="GB",
                    isoverage=False,
                    price_unit="GB",
                )
            ],
        )
        rate_plan.voice_mt = RatesPlanTest(
            rate_model=1,
            rates=[
                Rate(
                    range_from=0,
                    range_to=None,
                    value=Decimal("0.11"),
                    range_unit="GB",
                    isoverage=False,
                    price_unit="GB",
                )
            ],
        )
        rate_plan.voice_mo = RatesPlanTest(
            rate_model=1,
            rates=[
                Rate(
                    range_from=0,
                    range_to=None,
                    value=Decimal("0.10"),
                    range_unit="GB",
                    isoverage=False,
                    price_unit="GB",
                )
            ],
        )
        rate_plan.sms_mo = RatesPlanTest(
            rate_model=1,
            rates=[
                Rate(
                    range_from=0,
                    range_to=None,
                    value=Decimal("0.10"),
                    range_unit="GB",
                    isoverage=False,
                    price_unit="GB",
                )
            ],
        )
        return rate_plan

    @pytest.fixture
    def rate_plan_model(self):
        def factory(rate_plan_service):
            rate_model = [
                RatePlanModel(title="Model1", rate_model_code="Model1"),
                RatePlanModel(title="Model2", rate_model_code="Model2"),
                RatePlanModel(title="Model3", rate_model_code="Model3"),
                RatePlanModel(title="Model4", rate_model_code="Model4"),
            ]

            for r_model in rate_model:
                rate_plan_service.create_rate_plan_model(r_model)

        return factory

    @pytest.fixture
    def add_sims_to_active_in_month(self, *args, **kwargs):
        raise NotImplementedError

    @pytest.fixture
    def create_sim_card(self):
        def factory(number: int = 1, **kwargs):
            default = dict(
                iccid=ICCID.from_int(number),
                imsi=IMSI.from_int(number),
                msisdn=MSISDN.from_int(number),
                sim_status=SimStatus.ACTIVE,
            )
            data = {**default, **kwargs}
            sim_card = sim_model.SIMCard(**data)
            return sim_card

        return factory

    @pytest.fixture
    def create_sim_range(self, sim_repository):
        def factory(
            sim_cards: Iterable[sim_model.SIMCard],
            msisdn_pool: list[sim_model.MsisdnPool] | None = None,
        ):
            sim_list = list(sim_cards)
            range_ = sim_model.Range(
                title="Range 1",
                form_factor=sim_model.FormFactor.NANO,
                created_by="Test User",
                imsi_first=sim_list[0].imsi,
                imsi_last=sim_list[-1].imsi,
                remaining=len(sim_list),
                quantity=len(sim_list),
            )
            range_.sim_cards = sim_list
            sim_repository.add_range(range_, msisdn_pool=msisdn_pool)
            return range_

        return factory

    @pytest.fixture
    def allocate_sims(self, sim_repository):
        def factory(
            sim_range: sim_model.Range,
            rate_plan: RatePlan,
            sim_cards: Iterable[sim_model.SIMCard],
        ):
            sim_list = list(sim_cards)
            imsi_first, imsi_last = sim_list[0].imsi, sim_list[-1].imsi
            assert rate_plan.account_id
            assert sim_range.id
            allocation = Allocation(
                "a",
                rate_plan.account_id,
                sim_range.id,
                quantity=len(sim_list),
                rate_plan_id=rate_plan.id,
                imsi_first=imsi_first,
                imsi_last=imsi_last,
            )
            allocation.sim_cards = sim_list
            sim_repository.add_allocation(allocation)

        return factory


class BillingServiceContract(AbstractBillingServiceTestCase, ABC):
    def _get_usage_attrs_hash(self, invoice, billing_service):
        invoice = billing_service.get_invoice(invoice.id)
        usage_attrs = tuple((u.charge, u.volume) for u in invoice.usages)
        return hash(usage_attrs)

    def _generate_usage(self, imsis, month, seed=1):
        usage = []
        random.seed(seed)

        for i, imsi in enumerate(imsis):
            usage.extend(
                [
                    MonthlyUsageRecords(
                        imsi, s, "DUMMY", month, i * random.randint(20, 100)
                    )
                    for s in list(Service)
                ]
            )
        return usage

    def test_regenerate_unpublished_invoices(
        self,
        billing_service,
        usage_repository,
        rate_plan,
        create_sim_card,
        create_sim_range,
        allocate_sims,
        add_sims_to_active_in_month,
        rate_plan_model,
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,
                    allowance_used=Decimal(0),
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        sims = [create_sim_card(i) for i in range(1, 3)]
        sim_range = create_sim_range(sims)
        allocate_sims(sim_range, rate_plan, sims)

        month = Month.from_str("2023-01")
        add_sims_to_active_in_month(rate_plan, month, sims)
        usage = self._generate_usage([sim.imsi for sim in sims], month, seed=1)
        usage_repository.bulk_insert(usage)

        billing_service.generate_invoices(month)

        bc = billing_service.billing_cycles.get(month)
        assert (
            any(filter(lambda invoice: not invoice.is_published, bc._invoices.values()))
            is True
        )
        invoice = list(bc._invoices.values())[0]
        invoice = billing_service.get_invoice(invoice.id)

        usage_attrs_hash = self._get_usage_attrs_hash(invoice, billing_service)

        additional_usage = usage[1:]
        usage_repository.bulk_insert(additional_usage)
        billing_service.generate_invoices(month)
        bc = billing_service.billing_cycles.get(month)

        invoice = list(bc._invoices.values())[0]
        invoice = billing_service.get_invoice(invoice.id)
        updated_usage_attrs_hash = self._get_usage_attrs_hash(invoice, billing_service)

        assert updated_usage_attrs_hash != usage_attrs_hash

    def test_regenerate_published_invoices(
        self,
        billing_service,
        usage_repository,
        rate_plan,
        create_sim_card,
        create_sim_range,
        allocate_sims,
        add_sims_to_active_in_month,
        rate_plan_model,
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,  # type: ignore
                    allowance_used=Decimal(0),  # type: ignore
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        sims = [create_sim_card(i) for i in range(1, 3)]
        sim_range = create_sim_range(sims)
        allocate_sims(sim_range, rate_plan, sims)

        month = Month.from_str("2023-01")

        add_sims_to_active_in_month(rate_plan, month, sims)

        usage = self._generate_usage([sim.imsi for sim in sims], month, seed=1)
        usage_repository.bulk_insert(usage)

        billing_service.generate_invoices(month)
        bc = billing_service.billing_cycles.get(month)

        invoice = list(bc._invoices.values())[0]
        invoice = billing_service.get_invoice(invoice.id)

        usage_attrs_hash = self._get_usage_attrs_hash(invoice, billing_service)

        billing_service.publish_invoice([invoice.id])
        additional_usage = usage[1:]
        usage_repository.bulk_insert(additional_usage)
        billing_service.generate_invoices(month)
        bc = billing_service.billing_cycles.get(month)

        invoice = list(bc._invoices.values())[0]
        invoice = billing_service.get_invoice(invoice.id)
        updated_usage_attrs_hash = self._get_usage_attrs_hash(invoice, billing_service)
        assert updated_usage_attrs_hash == usage_attrs_hash

    def test_generate_after_failed_calculation_state_is_ok(
        self, billing_service, accounts, rate_plan_model, rate_plan
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,  # type: ignore
                    allowance_used=Decimal(0),  # type: ignore
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        month = Month.from_str("2023-04")
        bc = BillingCycle(month)
        invoice = bc.make_invoice(accounts[0].id, accounts[0].payment_terms)
        invoice.rating_state = InvoiceRatingState.FAILED
        billing_service.billing_cycles.add(bc)

        billing_service.generate_invoices(month)

        bc = billing_service.billing_cycles.get(month)

        assert len(bc.invoices) == len(accounts)
        assert bc.invoices[0].account_id == accounts[0].id

    def test_invoice_created_for_account(
        self, billing_service, accounts, rate_plan_model, rate_plan
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,  # type: ignore
                    allowance_used=Decimal(0),  # type: ignore
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        month = Month.from_str("2023-04")

        billing_service.generate_invoices(month)

        bc = billing_service.billing_cycles.get(month)
        assert len(bc.invoices) == len(accounts)
        assert bc.invoices[0].account_id == accounts[0].id

    def test_subscription_for_rate_plan_created(
        self, billing_service, rate_plan, rate_plan_model
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        month = Month.from_str("2023-04")

        rate_plan_details = billing_service.rate_plans.get(rate_plan_id)
        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan_details.id,
                    account_id=rate_plan_details.account_id,
                    name=rate_plan_details.name,
                    access_fee=rate_plan_details.access_fee,
                    is_default=rate_plan_details.is_default,
                    sim_limit=rate_plan_details.sim_limit,  # type: ignore
                    allowance_used=Decimal(0),  # type: ignore
                    _rate_groups=rate_plan_details._rate_groups,
                )
            ]
        )

        billing_service.generate_invoices(month)

        bc = billing_service.billing_cycles.get(month)
        invoice = bc.invoices[0]
        assert len(invoice.subscriptions) == 1
        subscription = invoice.subscriptions[0]
        assert subscription.rate_plan_id == rate_plan_id
        assert subscription.name == rate_plan.name
        assert subscription.access_fee == rate_plan.access_fee
        assert subscription.sims_total == 0

    def test_rate_plan_sim_cards_added_to_subscription(
        self,
        billing_service,
        rate_plan,
        create_sim_range,
        create_sim_card,
        add_sims_to_active_in_month,
        allocate_sims,
        rate_plan_model,
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,  # type: ignore
                    allowance_used=Decimal(0),  # type: ignore
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        active_sim = create_sim_card(1)
        inactive_sim = create_sim_card(2)
        sim_range = create_sim_range([active_sim, inactive_sim])
        allocate_sims(sim_range, rate_plan, [active_sim, inactive_sim])

        month = Month.from_str("2023-04")
        add_sims_to_active_in_month(rate_plan, month, [active_sim])

        billing_service.generate_invoices(month)

        bc = billing_service.billing_cycles.get(month)
        invoice = bc.invoices[0]
        assert len(invoice.subscriptions) == 1
        subscription = invoice.subscriptions[0]
        assert len(subscription.sims) == 1
        subscription_sim = subscription.sims[0]
        assert subscription_sim.iccid == active_sim.iccid
        assert subscription_sim.msisdn == active_sim.msisdn
        assert subscription_sim.imsi == active_sim.imsi
        assert subscription.sims_active == 1

    def test_usage_attached_to_subscription_sim(
        self,
        billing_service,
        create_sim_card,
        create_sim_range,
        rate_plan,
        usage_repository,
        add_sims_to_active_in_month,
        allocate_sims,
        rate_plan_model,
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,  # type: ignore
                    allowance_used=Decimal(0),  # type: ignore
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        sim_card = create_sim_card(1)
        sim_range = create_sim_range([sim_card])
        create_sim_range([sim_card])
        allocate_sims(sim_range, rate_plan, [sim_card])

        month = Month.from_str("2023-05")
        add_sims_to_active_in_month(rate_plan, month, [sim_card])
        # noise
        add_sims_to_active_in_month(rate_plan, month.prev(), [sim_card])
        add_sims_to_active_in_month(rate_plan, month.next(), [sim_card])

        usage_repository.bulk_insert(
            [
                MonthlyUsageRecords(
                    imsi=sim_card.imsi,
                    service=Service.DATA,
                    operator="oper1",
                    month=month,
                    volume=20 * GB,
                ),
                MonthlyUsageRecords(
                    imsi=sim_card.imsi,
                    service=Service.DATA,
                    operator="oper2",
                    month=month,
                    volume=30 * GB,
                ),
                # noise
                MonthlyUsageRecords(
                    imsi=sim_card.imsi,
                    service=Service.DATA,
                    operator="oper2",
                    month=month.prev(),
                    volume=90 * GB,
                ),
                MonthlyUsageRecords(
                    imsi=sim_card.imsi,
                    service=Service.DATA,
                    operator="oper2",
                    month=month.next(),
                    volume=90 * GB,
                ),
            ]
        )
        # noise
        billing_service.generate_invoices(month.prev())
        billing_service.generate_invoices(month.next())

        billing_service.generate_invoices(month)

        bc = billing_service.billing_cycles.get(month)
        invoice = bc.invoices[0]
        subscription = invoice.subscriptions[0]
        assert subscription.sims_active == 1
        assert subscription.charge == subscription.access_fee * subscription.sims_active
        subscription_sim = subscription.sims[0]
        assert len(subscription_sim.usage) == 1
        usage = subscription_sim.usage[0]
        assert usage.imsi == sim_card.imsi
        assert usage.service == Service.DATA
        assert usage.volume == 50 * GB
        rate_group = more_itertools.first_true(
            rate_plan.rate_groups, lambda rg: Service.DATA in rg.services
        )
        assert usage.charge == RatingCalculator.from_rate_group(rate_group).apply_rates(
            usage.volume, 1, Service.DATA
        )

    def test_invoice_new_sims_calculation(
        self,
        billing_service,
        rate_plan,
        create_sim_range,
        create_sim_card,
        add_sims_to_active_in_month,
        allocate_sims,
        rate_plan_model,
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,  # type: ignore
                    allowance_used=Decimal(0),  # type: ignore
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        first_activated = create_sim_card(1)
        previously_activated = create_sim_card(2)
        sims = [first_activated, previously_activated]
        sim_range = create_sim_range(sims)
        allocate_sims(sim_range, rate_plan, sims)

        month = Month.from_str("2023-04")
        add_sims_to_active_in_month(
            rate_plan, month, [first_activated], first_time_activated=False
        )
        add_sims_to_active_in_month(
            rate_plan,
            month,
            [previously_activated],
            first_time_activated=False,
        )
        # noise
        add_sims_to_active_in_month(
            rate_plan,
            month.prev(),
            [previously_activated],
            first_time_activated=False,
        )

        # noise
        billing_service.generate_invoices(month.prev())

        billing_service.generate_invoices(month)

        bc = billing_service.billing_cycles.get(month)
        invoice = bc.invoices[0]
        assert invoice.new_sims == 0

    @pytest.mark.parametrize(
        "ordering",
        [
            Ordering(field="imsi", order="ASC", model=SubscriptionSIM),
            Ordering(field="iccid", order="DESC", model=SubscriptionSIM),
        ],
    )
    def test_get_sim_details_with_ordering_and_search(
        self,
        billing_service,
        create_sim_card,
        create_sim_range,
        rate_plan,
        usage_repository,
        add_sims_to_active_in_month,
        allocate_sims,
        ordering,
        rate_plan_model,
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,  # type: ignore
                    allowance_used=Decimal(0),  # type: ignore
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        sim_cards = [create_sim_card(i) for i in range(10)]
        sim_range = create_sim_range(sim_cards)
        create_sim_range(sim_cards)
        allocate_sims(sim_range, rate_plan, sim_cards)

        month = Month.from_str("2023-05")
        add_sims_to_active_in_month(rate_plan, month, sim_cards)

        usage_repository.bulk_insert(
            [
                MonthlyUsageRecords(
                    imsi=random.choices(sim_cards)[0].imsi,
                    service=Service.DATA,
                    operator=f"op{i}",
                    month=month,
                    volume=random.randint(1, 50) * GB,
                )
                for i in range(20)
            ]
        )
        billing_service.generate_invoices(month)

        bc = billing_service.billing_cycles.get(month)
        invoice = bc.invoices[0]

        searching = Searching(search="007 008 009 0015", fields={"imsi", "msisdn"})
        subscription_sims, total = billing_service.get_sim_usage(
            invoice,
            searching=searching,
            ordering=ordering,
        )
        subscription_sims = list(subscription_sims)
        assert total == 3

        if ordering.order == "DESC":
            assert getattr(subscription_sims[0], ordering.field) > getattr(
                subscription_sims[-1], ordering.field
            )
            assert getattr(subscription_sims[0], ordering.field) > getattr(
                subscription_sims[1], ordering.field
            )
        else:
            assert getattr(subscription_sims[0], ordering.field) < getattr(
                subscription_sims[1], ordering.field
            )
            assert getattr(subscription_sims[0], ordering.field) < getattr(
                subscription_sims[-1], ordering.field
            )

    @pytest.mark.parametrize(
        "page_size,ordering,check_order",
        [
            (
                20,
                Ordering(field="imsi", order="DESC", model=SubscriptionSIM),
                lambda x: x.imsi,
            ),
            (
                100,
                Ordering(field="data_volume", order="ASC", model=SubscriptionSIM),
                lambda x: sum(
                    [i.volume for i in x.usage_summary if i.service == Service.DATA]
                ),
            ),
            (
                90,
                Ordering(field="sms_charge", order="DESC", model=SubscriptionSIM),
                lambda x: sum(
                    [
                        i.charge
                        for i in x.usage_summary
                        if i.service in (Service.SMS_MT, Service.SMS_MO)
                    ]
                ),
            ),
        ],
    )
    @pytest.mark.skip(
        reason=(
            "I need check real function working it is not generating subscription"
            " details which need to be fixed"
        )
    )
    def test_get_sim_details_with_ordering_and_pagination(
        self,
        billing_service,
        create_sim_card,
        create_sim_range,
        rate_plan,
        usage_repository,
        add_sims_to_active_in_month,
        allocate_sims,
        page_size,
        ordering,
        check_order,
        rate_plan_model,
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,  # type: ignore
                    allowance_used=Decimal(0),  # type: ignore
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        sim_cards = [create_sim_card(i) for i in range(100)]
        sim_range = create_sim_range(sim_cards)
        create_sim_range(sim_cards)
        allocate_sims(sim_range, rate_plan, sim_cards)

        month = Month.from_str("2023-05")
        add_sims_to_active_in_month(rate_plan, month, sim_cards)

        data_usage = [
            MonthlyUsageRecords(
                imsi=random.choices(sim_cards)[0].imsi,
                service=Service.DATA,
                operator=f"op{i}",
                month=month,
                volume=random.randint(1, 50) * GB,
            )
            for i in range(20)
        ]
        sms_mo_usage = [
            MonthlyUsageRecords(
                imsi=random.choices(sim_cards)[0].imsi,
                service=Service.SMS_MO,
                operator=f"op{i}",
                month=month,
                volume=random.randint(2, 1500),
            )
            for i in range(20)
        ]
        sms_mt_usage = [
            MonthlyUsageRecords(
                imsi=random.choices(sim_cards)[0].imsi,
                service=Service.SMS_MT,
                operator=f"op{i}",
                month=month,
                volume=random.randint(2, 200),
            )
            for i in range(20)
        ]

        usage_repository.bulk_insert(data_usage + sms_mo_usage + sms_mt_usage)
        billing_service.generate_invoices(month)

        bc = billing_service.billing_cycles.get(month)
        invoice = bc.invoices[0]

        subscription_sims, total = billing_service.get_sim_usage(
            invoice,
            pagination=Pagination(page=1, page_size=page_size),
            ordering=ordering,
        )
        subscription_sims = list(subscription_sims)
        assert total == 100
        assert len(subscription_sims) == page_size
        if ordering.order == "DESC":
            assert check_order(subscription_sims[0]) >= check_order(
                subscription_sims[-1]
            )
            assert check_order(subscription_sims[0]) >= check_order(
                subscription_sims[1]
            )
        else:
            assert check_order(subscription_sims[0]) <= check_order(
                subscription_sims[1]
            )
            assert check_order(subscription_sims[0]) <= check_order(
                subscription_sims[-1]
            )

    def test_if_invoice_generation_raises_exception_rating_state_set_to_failed(
        self, billing_service, account, monkeypatch
    ):
        month = Month.from_str("2023-04")

        def _generate_invoice_mock(*_, **__):
            raise AssertionError("generation failed")

        monkeypatch.setattr(
            billing_service,
            "generate_invoice",
            _generate_invoice_mock,
            raising=True,
        )

        billing_service.generate_invoices(month)

        bc = billing_service.billing_cycles.get(month)

        assert bc.invoices[0].rating_state == InvoiceRatingState.FAILED

    def test_error_in_one_invoice_does_not_stop_generation_of_other_invoices(
        self, billing_service, accounts, monkeypatch, rate_plan_model, rate_plan
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,  # type: ignore
                    allowance_used=Decimal(0),  # type: ignore
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        month = Month.from_str("2023-04")
        to_raise = iter([False, True, False])
        original_method = billing_service.generate_invoice

        def _generate_invoice_mock(*args, **kwargs):
            if next(to_raise):
                raise AssertionError("generation failed")
            else:
                return original_method(*args, **kwargs)

        monkeypatch.setattr(
            billing_service,
            "generate_invoice",
            _generate_invoice_mock,
            raising=True,
        )

        billing_service.generate_invoices(month)

        bc = billing_service.billing_cycles.get(month)
        assert len(bc.invoices) == len(accounts)

    def test_generate_invoices_for_given_account_ids(
        self, billing_service, accounts, rate_plan_model, rate_plan
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,  # type: ignore
                    allowance_used=Decimal(5.23),  # type: ignore
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        month = Month.from_str("2023-04")
        assert len(accounts) >= 3
        account_ids = [accounts[0].id, accounts[-1].id]
        billing_service.generate_invoices(month, account_ids)

        bc = billing_service.billing_cycles.get(month)
        assert len(bc.invoices) == 2
        assert bc.get_invoice(account_ids[0]) is not None
        assert bc.get_invoice(account_ids[1]) is not None

    def test_generate_invoices_new_for_given_account_ids_billing_error(
        self, billing_service, accounts, rate_plan_model, rate_plan
    ):
        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,  # type: ignore
                    allowance_used=Decimal(5.23),  # type: ignore
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        month = Month.from_str("2025-04")
        assert len(accounts) >= 3
        account_ids = [accounts[0].id, accounts[-1].id]
        billing_service.generate_invoices(month, account_ids)

        billing_service.billing_cycles.get(month)

        with pytest.raises(BillingCycleDoesNotExist):
            billing_service.get_invoices_new(Month(2025, 3, 1), account_ids[0])

    def test_generate_invoices_new_for_given_account_ids_success(
        self, billing_service, accounts, rate_plan_model, rate_plan
    ):

        billing_service.billing_cycles.reset_invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_account_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.reset_invoice_overage_charge = MagicMock(
            return_value=None
        )
        billing_service.billing_cycles.invoice_overage_charge = MagicMock(
            return_value=None
        )

        rate_plan_model(billing_service.rate_plans)
        rate_plan_id = billing_service.rate_plans.add(rate_plan)
        rate_plan = billing_service.rate_plans.get(rate_plan_id)

        billing_service.rate_plans.query = MagicMock(
            return_value=[
                RatePlans(
                    _id=rate_plan.id,
                    account_id=rate_plan.account_id,
                    name=rate_plan.name,
                    access_fee=rate_plan.access_fee,
                    is_default=rate_plan.is_default,
                    sim_limit=rate_plan.sim_limit,  # type: ignore
                    allowance_used=Decimal(5.23),  # type: ignore
                    _rate_groups=rate_plan._rate_groups,
                )
            ]
        )

        month = Month.from_str("2023-04")
        assert len(accounts) >= 3
        account_ids = [accounts[0].id, accounts[-1].id]
        billing_service.generate_invoices(month, account_ids)

        bc = billing_service.billing_cycles.get(month)

        billing_service.billing_cycles.get_invoices_new = MagicMock(
            return_value=bc.invoices
        )

        response = billing_service.get_invoices_new(month, account_ids[0])

        assert len(response) == 2


class BillingServiceInMemoryFixtureMixin(AbstractBillingServiceTestCase):
    @pytest.fixture
    def usage_repository(self):
        return InMemoryUsageRepository()

    @pytest.fixture
    def sim_repository(self):
        return InMemorySimRepository()

    @pytest.fixture
    def account_repository(self):
        return InMemoryAccountRepository()

    @pytest.fixture
    def billing_service(
        self, usage_repository, sim_repository, account_repository
    ) -> BillingService:
        billing_cycle_repository = InMemoryBillingCycleRepository()
        return BillingService(
            account_repository,
            billing_cycle_repository,
            InMemoryRatePlanRepository([]),
            sim_repository,
            SimUsageService(usage_repository, billing_cycle_repository),
        )

    @pytest.fixture
    def add_sims_to_active_in_month(self, sim_repository: InMemorySimRepository):
        def factory(
            rate_plan: RatePlan,
            month: Month,
            sim_cards: Iterable[sim_model.SIMCard],
            first_time_activated: bool = False,
        ):
            sim_repository.add_card_active_statistic(
                [
                    {
                        "id": sim.id,
                        "account_id": rate_plan.account_id,
                        "iccid": sim.iccid,
                        "imsi": sim.imsi,
                        "msisdn": sim.msisdn,
                        "sim_status": "Active",
                        "is_first_activation": first_time_activated,
                        "rate_plan_id": rate_plan.id,
                        "month": month,
                        "usage": 0,
                    }
                    for sim in sim_cards
                ]
            )

        return factory


class TestBillingService(BillingServiceInMemoryFixtureMixin, BillingServiceContract):
    ...


class TestBillingServiceDetails:
    def test_invoice_reconciliation(self):

        mock_account_repository = MagicMock(spec=AbstractAccountRepository)
        mock_billing_cycle_repository = MagicMock(spec=AbstractBillingCycleRepository)
        mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
        mock_sim_repository = MagicMock(spec=AbstractSimRepository)
        mock_sim_usage_service = MagicMock(spec=AbstractSimUsageService)

        reconciliation_data = [
            ReconciliationAgg(**SIM_DATA_1),
            ReconciliationAgg(**SIM_DATA_2),
            ReconciliationAgg(**SIM_DATA_3),
            ReconciliationAgg(**SIM_DATA_4),
            ReconciliationAgg(**SIM_DATA_5),
            ReconciliationAgg(**SIM_DATA_6),
            ReconciliationAgg(**SIM_DATA_7),
            ReconciliationAgg(**SIM_DATA_8),
        ]

        month = Month(2023, 5, 1)
        mock_billing_cycle_repository.get.return_value = BillingCycle(month=month)
        mock_billing_cycle_repository.get_invoice_reconciliation.return_value = (
            reconciliation_data
        )

        billing_service_mock = BillingService(
            account_repository=mock_account_repository,
            billing_cycle_repository=mock_billing_cycle_repository,
            rate_plan_repository=mock_rate_plan_repository,
            sim_repository=mock_sim_repository,
            sim_usage_service=mock_sim_usage_service,
        )

        result = billing_service_mock.invoice_reconciliation(month)

        assert isinstance(result[0], ReconciliationDetails)
        assert isinstance(result[1], ReconciliationDetails)

    def test_invoice_reconciliation_success_all_match(self):

        mock_account_repository = MagicMock(spec=AbstractAccountRepository)
        mock_billing_cycle_repository = MagicMock(spec=AbstractBillingCycleRepository)
        mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
        mock_sim_repository = MagicMock(spec=AbstractSimRepository)
        mock_sim_usage_service = MagicMock(spec=AbstractSimUsageService)

        reconciliation_data = []
        month = Month(2023, 5, 1)
        mock_billing_cycle_repository.get.return_value = BillingCycle(month=month)
        mock_billing_cycle_repository.get_invoice_reconciliation.return_value = (
            reconciliation_data
        )

        billing_service_mock = BillingService(
            account_repository=mock_account_repository,
            billing_cycle_repository=mock_billing_cycle_repository,
            rate_plan_repository=mock_rate_plan_repository,
            sim_repository=mock_sim_repository,
            sim_usage_service=mock_sim_usage_service,
        )

        result = billing_service_mock.invoice_reconciliation(month)

        assert result == "Success: all the data matched."

    def test_invoice_reconciliation_bill_cycle_does_not_exist(self):

        mock_account_repository = MagicMock(spec=AbstractAccountRepository)
        mock_billing_cycle_repository = MagicMock(spec=AbstractBillingCycleRepository)
        mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
        mock_sim_repository = MagicMock(spec=AbstractSimRepository)
        mock_sim_usage_service = MagicMock(spec=AbstractSimUsageService)

        reconciliation_data = []
        month = Month(2023, 5, 1)
        error_month = Month(2023, 7, 1)
        mock_billing_cycle_repository.get.return_value = BillingCycle(month=month)
        mock_billing_cycle_repository.get.side_effect = BillingCycleDoesNotExist()
        mock_billing_cycle_repository.get_invoice_reconciliation.return_value = (
            reconciliation_data
        )

        billing_service_mock = BillingService(
            account_repository=mock_account_repository,
            billing_cycle_repository=mock_billing_cycle_repository,
            rate_plan_repository=mock_rate_plan_repository,
            sim_repository=mock_sim_repository,
            sim_usage_service=mock_sim_usage_service,
        )

        with pytest.raises(BillingCycleDoesNotExist):
            billing_service_mock.invoice_reconciliation(error_month)

    def test_invoice_reconciliation_bill_cycle_does_not_exist_error(self):

        mock_account_repository = MagicMock(spec=AbstractAccountRepository)
        mock_billing_cycle_repository = MagicMock(spec=AbstractBillingCycleRepository)
        mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
        mock_sim_repository = MagicMock(spec=AbstractSimRepository)
        mock_sim_usage_service = MagicMock(spec=AbstractSimUsageService)

        reconciliation_data = []
        month = Month(2023, 5, 1)
        mock_billing_cycle_repository.get.return_value = None
        mock_billing_cycle_repository.get_invoice_reconciliation.return_value = (
            reconciliation_data
        )

        billing_service_mock = BillingService(
            account_repository=mock_account_repository,
            billing_cycle_repository=mock_billing_cycle_repository,
            rate_plan_repository=mock_rate_plan_repository,
            sim_repository=mock_sim_repository,
            sim_usage_service=mock_sim_usage_service,
        )

        with pytest.raises(BillingCycleDoesNotExist):
            billing_service_mock.invoice_reconciliation(month)

    def test_get_monthly_reconciliation(self):

        mock_account_repository = MagicMock(spec=AbstractAccountRepository)
        mock_billing_cycle_repository = MagicMock(spec=AbstractBillingCycleRepository)
        mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
        mock_sim_repository = MagicMock(spec=AbstractSimRepository)
        mock_sim_usage_service = MagicMock(spec=AbstractSimUsageService)

        reconciliation_data = [
            ReconciliationAgg(**SIM_DATA_1),
            ReconciliationAgg(**SIM_DATA_2),
            ReconciliationAgg(**SIM_DATA_3),
            ReconciliationAgg(**SIM_DATA_4),
            ReconciliationAgg(**SIM_DATA_5),
            ReconciliationAgg(**SIM_DATA_6),
            ReconciliationAgg(**SIM_DATA_7),
            ReconciliationAgg(**SIM_DATA_8),
        ]

        month = Month(2023, 5, 1)
        mock_billing_cycle_repository.get.return_value = BillingCycle(month=month)
        mock_billing_cycle_repository.get_monthly_reconciliation.return_value = (
            reconciliation_data
        )

        billing_service_mock = BillingService(
            account_repository=mock_account_repository,
            billing_cycle_repository=mock_billing_cycle_repository,
            rate_plan_repository=mock_rate_plan_repository,
            sim_repository=mock_sim_repository,
            sim_usage_service=mock_sim_usage_service,
        )

        result = billing_service_mock.get_monthly_reconciliation(month)

        assert isinstance(result[0], ReconciliationDetails)
        assert isinstance(result[1], ReconciliationDetails)

    def test_get_monthly_reconciliation_success_all_match(self):

        mock_account_repository = MagicMock(spec=AbstractAccountRepository)
        mock_billing_cycle_repository = MagicMock(spec=AbstractBillingCycleRepository)
        mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
        mock_sim_repository = MagicMock(spec=AbstractSimRepository)
        mock_sim_usage_service = MagicMock(spec=AbstractSimUsageService)

        reconciliation_data = []
        month = Month(2023, 5, 1)
        mock_billing_cycle_repository.get.return_value = BillingCycle(month=month)
        mock_billing_cycle_repository.get_monthly_reconciliation.return_value = (
            reconciliation_data
        )

        billing_service_mock = BillingService(
            account_repository=mock_account_repository,
            billing_cycle_repository=mock_billing_cycle_repository,
            rate_plan_repository=mock_rate_plan_repository,
            sim_repository=mock_sim_repository,
            sim_usage_service=mock_sim_usage_service,
        )

        result = billing_service_mock.get_monthly_reconciliation(month)

        assert result == "Success: all the data matched."

    def test_get_monthly_reconciliation_bill_cycle_does_not_exist(self):

        mock_account_repository = MagicMock(spec=AbstractAccountRepository)
        mock_billing_cycle_repository = MagicMock(spec=AbstractBillingCycleRepository)
        mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
        mock_sim_repository = MagicMock(spec=AbstractSimRepository)
        mock_sim_usage_service = MagicMock(spec=AbstractSimUsageService)

        reconciliation_data = []
        month = Month(2023, 5, 1)
        error_month = Month(2023, 7, 1)
        mock_billing_cycle_repository.get.return_value = BillingCycle(month=month)
        mock_billing_cycle_repository.get.side_effect = BillingCycleDoesNotExist()
        mock_billing_cycle_repository.get_monthly_reconciliation.return_value = (
            reconciliation_data
        )

        billing_service_mock = BillingService(
            account_repository=mock_account_repository,
            billing_cycle_repository=mock_billing_cycle_repository,
            rate_plan_repository=mock_rate_plan_repository,
            sim_repository=mock_sim_repository,
            sim_usage_service=mock_sim_usage_service,
        )

        with pytest.raises(BillingCycleDoesNotExist):
            billing_service_mock.get_monthly_reconciliation(error_month)

    def test_get_monthly_reconciliation_bill_cycle_does_not_exist_error(self):

        mock_account_repository = MagicMock(spec=AbstractAccountRepository)
        mock_billing_cycle_repository = MagicMock(spec=AbstractBillingCycleRepository)
        mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
        mock_sim_repository = MagicMock(spec=AbstractSimRepository)
        mock_sim_usage_service = MagicMock(spec=AbstractSimUsageService)

        reconciliation_data = []
        month = Month(2023, 5, 1)
        mock_billing_cycle_repository.get.return_value = None
        mock_billing_cycle_repository.get_monthly_reconciliation.return_value = (
            reconciliation_data
        )

        billing_service_mock = BillingService(
            account_repository=mock_account_repository,
            billing_cycle_repository=mock_billing_cycle_repository,
            rate_plan_repository=mock_rate_plan_repository,
            sim_repository=mock_sim_repository,
            sim_usage_service=mock_sim_usage_service,
        )

        with pytest.raises(BillingCycleDoesNotExist):
            billing_service_mock.get_monthly_reconciliation(month)
